import json

from django.contrib import admin
from django.contrib.auth.password_validation import password_validators_help_text_html
from django.db.models import J<PERSON><PERSON><PERSON>
from django.forms import widgets
from django_json_widget.widgets import JSONEditorWidget


class UserAdminMixin:
    exclude = ["password_hash"]
    readonly_fields = ["last_login", "created_at", "modified_at"]
    list_display = [
        "email",
        "first_name",
        "last_name",
        "last_login",
        "is_active",
    ]
    search_fields = ["first_name", "last_name", "email"]
    date_hierarchy = "created_at"
    list_filter = ["is_active"]

    def response_add(self, request, obj, post_url_continue=None):
        new_password = request.POST.get("password")
        if new_password:
            obj.set_password(new_password)
            obj.save()
        return super().response_add(request, obj, post_url_continue)

    def response_change(self, request, obj):
        new_password = request.POST["password"]
        if new_password:
            obj.set_password(new_password)
            obj.save()
        return super().response_change(request, obj)

    def change_view(self, request, object_id, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context["password_validators_help_text_html"] = (
            password_validators_help_text_html()
        )
        return super().change_view(
            request, object_id, form_url, extra_context=extra_context
        )


class BaseModelAdmin(admin.ModelAdmin):
    pass


class PrettyJSONWidget(widgets.Textarea):
    """
    simple JSON widget for pretty printing JSON in textarea (default admin widget).
    no additional dependencies required.
    https://stackoverflow.com/a/52627264
    """

    INDENT = 4

    def format_value(self, value):
        try:
            value = json.dumps(json.loads(value), indent=self.INDENT, sort_keys=True)
            # these lines will try to adjust size of TextArea to fit to content
            row_lengths = [len(r) for r in value.split("\n")]
            self.attrs["rows"] = min(max(len(row_lengths) + 2, 10), 30)
            self.attrs["cols"] = 62  #  min(max(max(row_lengths) + 2, 40), 120)
        except Exception:  # noqa: BLE001
            return super().format_value(value)
        else:
            return value


class PrettyJSONMixin:
    formfield_overrides = {JSONField: {"widget": PrettyJSONWidget}}


class JSONEditorMixin:
    """
    Mixin that provides JSONEditorWidget for JSONField fields in Django admin.
    Supports fold/unfold, editing, and search functionality for JSON data.
    """

    formfield_overrides = {
        JSONField: {
            "widget": JSONEditorWidget(
                options={
                    "modes": ["tree", "code", "form", "text", "view"],
                    "mode": "tree",
                    "search": True,
                    "history": True,
                    "navigationBar": True,
                    "statusBar": True,
                    "mainMenuBar": True,
                    "enableSort": True,
                    "enableTransform": True,
                },
                width="100%",
                height="400px",
            )
        }
    }
