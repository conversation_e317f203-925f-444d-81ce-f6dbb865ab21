import pytest
from django.contrib import admin
from django.db import models
from django.test import TestCase
from django_json_widget.widgets import JSONEditorWidget

from common.admin import J<PERSON>NEditor<PERSON><PERSON>in, PrettyJSONM<PERSON>in, PrettyJSONWidget
from common.db import BaseModel


class TestModel(BaseModel):
    """Test model with JSONField for testing admin mixins."""

    json_data = models.JSONField(default=dict, blank=True)

    class Meta:
        app_label = "common"


class TestJSONEditorMixin(TestCase):
    """Test JSONEditorMixin functionality."""

    def test_json_editor_mixin_formfield_overrides(self):
        """Test that JSONEditorMixin correctly overrides J<PERSON><PERSON>ield widget."""

        class TestAdmin(JSONEditorMixin, admin.ModelAdmin):
            pass

        # Check that formfield_overrides is set correctly
        assert hasattr(TestAdmin, "formfield_overrides")
        assert models.<PERSON><PERSON><PERSON><PERSON> in TestAdmin.formfield_overrides

        # Check that the widget is JSONEditorWidget
        widget_config = TestAdmin.formfield_overrides[models.J<PERSON><PERSON>ield]
        assert "widget" in widget_config
        widget = widget_config["widget"]
        assert isinstance(widget, JSONEditorWidget)

    def test_json_editor_widget_options(self):
        """Test that JSONEditorWidget has correct options configured."""

        class TestAdmin(JSONEditorMixin, admin.ModelAdmin):
            pass

        widget = TestAdmin.formfield_overrides[models.JSONField]["widget"]

        # Check widget dimensions
        assert widget.width == "100%"
        assert widget.height == "400px"

        # Check that options are set
        assert hasattr(widget, "options")
        options = widget.options

        # Check key options for functionality
        assert "modes" in options
        assert "tree" in options["modes"]
        assert "code" in options["modes"]
        assert "form" in options["modes"]
        assert "text" in options["modes"]
        assert "view" in options["modes"]

        assert options.get("mode") == "tree"
        assert options.get("search") is True
        assert options.get("history") is True
        assert options.get("navigationBar") is True
        assert options.get("statusBar") is True
        assert options.get("mainMenuBar") is True
        assert options.get("enableSort") is True
        assert options.get("enableTransform") is True
        assert options.get("theme") == "dark"

        # Check widget attributes for styling
        assert hasattr(widget, "attrs")
        assert "style" in widget.attrs
        assert "background-color: #2d2d2d" in widget.attrs["style"]


class TestPrettyJSONMixin(TestCase):
    """Test PrettyJSONMixin functionality for comparison."""

    def test_pretty_json_mixin_formfield_overrides(self):
        """Test that PrettyJSONMixin correctly overrides JSONField widget."""

        class TestAdmin(PrettyJSONMixin, admin.ModelAdmin):
            pass

        # Check that formfield_overrides is set correctly
        assert hasattr(TestAdmin, "formfield_overrides")
        assert models.JSONField in TestAdmin.formfield_overrides

        # Check that the widget is PrettyJSONWidget
        widget_config = TestAdmin.formfield_overrides[models.JSONField]
        assert "widget" in widget_config
        widget_class = widget_config["widget"]
        assert widget_class == PrettyJSONWidget


@pytest.mark.django_db
def test_json_editor_mixin_integration():
    """Integration test to ensure JSONEditorMixin works with Django admin."""

    # Create a test admin class using JSONEditorMixin
    class TestModelAdmin(JSONEditorMixin, admin.ModelAdmin):
        pass

    # Verify the admin class can be instantiated
    admin_instance = TestModelAdmin(TestModel, admin.site)

    # Verify formfield_overrides is properly configured
    assert models.JSONField in admin_instance.formfield_overrides
    widget = admin_instance.formfield_overrides[models.JSONField]["widget"]
    assert isinstance(widget, JSONEditorWidget)

    # Test that the widget can be rendered (basic functionality check)
    assert hasattr(widget, "render")
    assert callable(widget.render)


def test_json_editor_widget_import():
    """Test that JSONEditorWidget can be imported successfully."""
    from django_json_widget.widgets import JSONEditorWidget

    # Create widget instance
    widget = JSONEditorWidget()
    assert widget is not None

    # Test with custom options
    custom_widget = JSONEditorWidget(
        options={"mode": "code", "search": False}, width="50%", height="300px"
    )
    assert custom_widget.width == "50%"
    assert custom_widget.height == "300px"
    assert custom_widget.options["mode"] == "code"
    assert custom_widget.options["search"] is False
