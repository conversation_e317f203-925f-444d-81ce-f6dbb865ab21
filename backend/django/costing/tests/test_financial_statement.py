from unittest.mock import patch

import pytest
from django.contrib import admin
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import models
from django_json_widget.widgets import JSONEditorWidget

from costing.admin.financial_statement import FinancialStatementAdmin
from costing.models import FinancialStatement, MediaFile


@pytest.mark.django_db
def test_financial_statement_csv_parsing():
    # Create a simple CSV file
    csv_content = b"""Bilancio;
Voce;Valore;
Ricavi;1000000;
Costi;800000;
Utile;200000;"""

    csv_file = SimpleUploadedFile("test.csv", csv_content)

    # Create MediaFile instance
    csv_media = MediaFile.objects.create(file=csv_file)

    # Create a FinancialStatement instance with the MediaFile reference
    financial_statement = FinancialStatement.objects.create(csv_file=csv_media)

    # Test parse_csv method
    financial_statement.parse_csv()
    assert financial_statement.csv_data is not None
    assert len(financial_statement.csv_data) == 3  # 3 rows in the CSV
    assert financial_statement.csv_data[0]["Voce"] == "Ricavi"
    assert financial_statement.csv_data[0]["Valore"] == "1000000"


@pytest.mark.django_db
def test_financial_statement_xbrl_parsing():
    # Create a realistic XBRL file with sample data
    xbrl_content = b"""<?xml version="1.0" encoding="UTF-8"?>
<xbrl xmlns="http://www.xbrl.org/2003/instance" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:itcc-ci="http://www.infocamere.it/itnn/fr/itcc/ci/2018-11-04" xmlns:itcc-ci-abb="http://www.infocamere.it/itnn/fr/itcc/ci/abb/2018-11-04">  # noqa: E501
    <link:schemaRef xlink:type="simple" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" xlink:href="itcc-ci-abb-2018-11-04.xsd"/>  # noqa: E501
<!--XBRLCOMPILER;COMPNAME= MIDA4 S.r.l.;COMPCODE=00797300324;TIME=2024-05-06 - 12:27:22 -->  # noqa: E501
    <context id="IstantEserPrec">
        <entity>
            <identifier scheme="http://www.infocamere.it">12345678901</identifier>
        </entity>
        <period>
            <instant>2022-12-13</instant>
        </period>
        <scenario>
            <itcc-ci:scen>itcc-ci:Depositato</itcc-ci:scen>
        </scenario>
    </context>
    <context id="DurationEserPrec">
        <entity>
            <identifier scheme="http://www.infocamere.it">12345678901</identifier>
        </entity>
        <period>
            <startDate>2021-12-14</startDate>
            <endDate>2022-12-13</endDate>
        </period>
        <scenario>
            <itcc-ci:scen>itcc-ci:Depositato</itcc-ci:scen>
        </scenario>
    </context>
    <context id="IstantEserCorr">
        <entity>
            <identifier scheme="http://www.infocamere.it">12345678901</identifier>
        </entity>
        <period>
            <instant>2023-12-31</instant>
        </period>
        <scenario>
            <itcc-ci:scen>itcc-ci:Depositato</itcc-ci:scen>
        </scenario>
    </context>
    <context id="DurationEserCorr">
        <entity>
            <identifier scheme="http://www.infocamere.it">12345678901</identifier>
        </entity>
        <period>
            <startDate>2022-12-14</startDate>
            <endDate>2023-12-31</endDate>
        </period>
        <scenario>
            <itcc-ci:scen>itcc-ci:Depositato</itcc-ci:scen>
        </scenario>
    </context>
    <unit id="Valuta">
        <measure>iso4217:EUR</measure>
    </unit>
    <unit id="shares">
        <measure>shares</measure>
    </unit>
    <unit id="pure">
        <measure>pure</measure>
    </unit>
     <itcc-ci:TotaleCrediti contextRef="IstantEserCorr" unitRef="Valuta" decimals="0">131821</itcc-ci:TotaleCrediti>  # noqa: E501
     <itcc-ci:CreditiEsigibiliEntroEsercizioSuccessivo contextRef="IstantEserCorr" unitRef="Valuta" decimals="0">131821</itcc-ci:CreditiEsigibiliEntroEsercizioSuccessivo>  # noqa: E501
     <itcc-ci:TotaleDisponibilitaLiquide contextRef="IstantEserCorr" unitRef="Valuta" decimals="0">3726054</itcc-ci:TotaleDisponibilitaLiquide>  # noqa: E501
</xbrl>"""

    xbrl_file = SimpleUploadedFile("test.xbrl", xbrl_content)

    # Create MediaFile instance
    xbrl_media = MediaFile.objects.create(file=xbrl_file)

    # Create a FinancialStatement instance with the MediaFile reference
    financial_statement = FinancialStatement.objects.create(xbrl_file=xbrl_media)

    # Create a mock for the xbrl_data that would be extracted
    expected_data = {
        "TotaleCrediti": 131821,
        "CreditiEsigibiliEntroEsercizioSuccessivo": 131821,
        "TotaleDisponibilitaLiquide": 3726054,
        "entity": {"identifier": "12345678901"},
        "period": {"instant": "2023-12-31"},
    }

    # Patch the parse_xbrl method to avoid actual XBRL parsing
    with patch.object(
        FinancialStatement, "parse_xbrl", autospec=True
    ) as mock_parse_xbrl:
        # Configure the mock to set xbrl_data and call save
        def side_effect(self):
            self.xbrl_data = expected_data
            self.save(update_fields=["xbrl_data"])

        mock_parse_xbrl.side_effect = side_effect

        # Call the parse_xbrl method
        financial_statement.parse_xbrl()

        # Verify the mock was called
        mock_parse_xbrl.assert_called_once_with(financial_statement)

        # Refresh from database
        financial_statement.refresh_from_db()

        # Verify the data was saved to the model
        assert financial_statement.xbrl_data is not None
        assert financial_statement.xbrl_data["TotaleCrediti"] == 131821
        assert financial_statement.xbrl_data["TotaleDisponibilitaLiquide"] == 3726054


def test_financial_statement_admin_uses_json_editor_mixin():
    """Test that FinancialStatementAdmin uses JSONEditorMixin for JSON fields."""

    # Check that FinancialStatementAdmin has formfield_overrides
    assert hasattr(FinancialStatementAdmin, "formfield_overrides")

    # Check that JSONField is overridden
    assert models.JSONField in FinancialStatementAdmin.formfield_overrides

    # Check that the widget is JSONEditorWidget
    widget_config = FinancialStatementAdmin.formfield_overrides[models.JSONField]
    assert "widget" in widget_config
    widget = widget_config["widget"]
    assert isinstance(widget, JSONEditorWidget)

    # Check that the widget has the expected configuration
    assert widget.width == "100%"
    assert widget.height == "400px"

    # Check key options for JSON editing functionality
    options = widget.options
    assert "modes" in options
    assert "tree" in options["modes"]
    assert "code" in options["modes"]
    assert options.get("search") is True
    assert options.get("history") is True


@pytest.mark.django_db
def test_financial_statement_admin_integration():
    """Integration test for FinancialStatementAdmin with JSONEditorMixin."""

    # Create a FinancialStatement instance with JSON data
    test_json_data = {
        "test_key": "test_value",
        "nested": {"array": [1, 2, 3], "boolean": True},
    }

    FinancialStatement.objects.create(
        xbrl_data=test_json_data, csv_data={"csv_test": "data"}
    )

    # Create admin instance
    admin_instance = FinancialStatementAdmin(FinancialStatement, admin.site)

    # Verify the admin can handle the model with JSON data
    assert admin_instance.model == FinancialStatement

    # Verify formfield_overrides is working
    assert models.JSONField in admin_instance.formfield_overrides

    # Test that the widget can be used (basic functionality)
    widget = admin_instance.formfield_overrides[models.JSONField]["widget"]
    assert hasattr(widget, "render")
    assert callable(widget.render)
